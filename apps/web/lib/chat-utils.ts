import { ChatMessage, Content, ContentType, Conversation } from "@/service/session-service";

/**
 * 将历史对话数据转换为聊天消息格式
 * @param history 历史对话数组
 * @returns 格式化后的消息数组
 */
export const convertHistoryToMessages = (
  history: Conversation[]
): any[] => {
  const result: any[] = [];

  const reversed = [...history].reverse();

  reversed
  .forEach((item) => {
    const question = item?.question;
    const answers = item?.answers || [];

    // 处理 question
    if (question) {
      const { isContent, textContent, references, files } = contentHistoryFmt(
        question.content
      );

      // 只有当 isContent 为 true 时才加入数组
      if (isContent) {
        result.push({
          id: question.id,
          role: question.type === "USER" ? "user" : "assistant",
          content: textContent.join(","),
          references,
          files,
        });
      }
    }

    // 处理 answers 数组
    answers.forEach((answer) => {
      const { isContent, textContent, references, files } = contentHistoryFmt(
        answer.content
      );

      // 只有当 isContent 为 true 时才加入数组
      if (isContent) {
        result.push({
          id: answer.id,
          role: answer.type === "USER" ? "user" : "assistant",
          content: textContent.join(","),
          references,
          files,
        });
      } else {
        result.push({
          id: answer.id,
          role: answer.type === "USER" ? "user" : "assistant",
          content: "",
          references,
          files,
        });
      }
    });
  });

  return result;
};

/**
 * 格式化内容历史数据
 * @param content 内容数组
 * @returns 格式化后的内容对象，包含文本、引用、文件等分类数据
 */
export const contentHistoryFmt = (content?: string) => {
  if (!content)
    return {
      isContent: false,
      textContent: [],
      references: [],
      files: [],
    };
const contentFmt = JSON.parse(content) as Content[]

  // 按类型分类内容
  const texts = contentFmt.filter(({ type }) => type === ContentType.Text);
  const references = contentFmt.filter(
    ({ type }) => type === ContentType.Reference
  );
  const files = contentFmt.filter(({ type }) => type === ContentType.File);

  // 判断是否有有效内容（至少有文本内容）
  const isContent = !!texts.length;

  // 提取文本内容
  const textContent = texts?.map(({ content }) => content);

  return {
    isContent,
    textContent,
    references,
    files,
  };
};

// templateMessage id为template-question-> question_id
// 

export function deduplicateArray(
  arr: ChatMessage[],
  id: string | null | undefined,
) {
  const deduplicatedMap = new Map();

  // Loop through the array and store the last occurrence of each message_id or messageId in the map
  // eslint-disable-next-line no-restricted-syntax
  for (const obj of arr) {
    const key = obj.id || "template-answer";

    if ((id && key === id) || key === "template-answer") {
      deduplicatedMap.delete("template-answer");
    }

    deduplicatedMap.set(key, obj);
  }

  // Convert the map values back to an array to get the deduplicated result
  const result = Array.from(deduplicatedMap.values());

  return result;
}