"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { prism } from "react-syntax-highlighter/dist/esm/styles/prism";

import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import remarkMath from 'remark-math';
import { cn } from '@ragtop-web/ui/lib/utils'
import { useCallback, useMemo, useState } from 'react'
import { Button } from '@ragtop-web/ui/components/button'
import { Popover, PopoverContent, PopoverTrigger } from '@ragtop-web/ui/components/popover'
import { Dialog, DialogContent, DialogTrigger } from '@ragtop-web/ui/components/dialog'
import { FileText, ExternalLink, ZoomIn } from 'lucide-react'
import { PdfDrawer } from './pdf-drawer';
import { flow } from 'lodash-es';
// 注意：如果样式导入有问题，可以注释掉下面这行
// import 'highlight.js/styles/github.css'

interface MarkdownRendererProps {
  content: string
  className?: string
  references?: any[]
  files?: any[]
}


// 正常的Latex公式输出是$$content$$, 但是在gpt输出中使用的时候是\[\]或者\(\)，需要做兼容
// https://github.com/remarkjs/react-markdown/issues/785
function escapeDollarNumber(text: string): string {
  let isInBlockCode = false;

  return text
    .split("\n")
    .map((line) => {
      if (line.includes("```") || line.includes("`")) {
        isInBlockCode = !isInBlockCode;

        return line;
      }

      if (!isInBlockCode) {
        return line
          .split(/(`.*?`)/g)
          .map((segment, index) => {
            if (index % 2 === 0) {
              return segment.replace(/(\$)/g, (match, offset, string) => {
                // 检查前一个字符是否是 ` 或 \
                if (
                  offset > 0 && (string[offset - 1] === "`" || string[offset - 1] === "\\")
                ) {
                  return match; // 如果前一个字符是 ` 或 \，则不替换
                }

                return `\\${match}`; // 否则，替换为 \$
              });
            }

            return segment;
          })
          .join("");
      }

      return line;
    })
    .join("\n");
}

export const preprocessLaTeX = (content: string) => {
  // Escape $ to \$ to prevent conflicts with inline LaTeX
  let contentFmt = escapeDollarNumber(content);

  // Replace block-level LaTeX delimiters \[ \] with $$ $$
  const blockProcessedContent = contentFmt.replace(
    /\\\[(.*?)\\\]/gs,
    (_, equation) => `$$${equation}$$`,
  );
  // Replace inline LaTeX delimiters \( \) with $ $
  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\((.*?)\\\)/gs,
    (_, equation) => `$${equation}$`,
  );

  return inlineProcessedContent;
};


// ragflow代码中包含了，暂时不知道什么意思
export function replaceThinkToSection(text: string = '') {
  const pattern = /<think>([\s\S]*?)<\/think>/g;

  const result = text.replace(pattern, '<section class="think">$1</section>');

  return result;
}

/**
 * 处理引用格式 ##3$$ 的正则表达式
 */
const referenceRegex = /##(\d+)\$\$/g;

/**
 * 获取引用索引
 */
const getReferenceIndex = (match: string) => {
  const indexMatch = match.match(/##(\d+)\$\$/);
  return indexMatch && indexMatch[1] ? parseInt(indexMatch[1], 10) : -1;
};


/**
 * Markdown 渲染器组件
 *
 * 支持 GitHub Flavored Markdown 和代码高亮
 */

export function MarkdownRenderer({ content, className, references = [], files = [] }: MarkdownRendererProps) {
  const [pdfPreviewOpen, setPdfPreviewOpen] = useState(false)
  const [currentChunk, setCurrentChunk] = useState<any>(null)
  const [currentPdfUrl, setCurrentPdfUrl] = useState('')
  const [currentPdfName, setCurrentPdfName] = useState('')


   const contentWithCursor = useMemo(() => {

   const calculate = flow(replaceThinkToSection, preprocessLaTeX); 
  
    return calculate(content);
  }, [content]);

  /**
   * 获取引用信息
   */
  const getReferenceInfo = useCallback((index: number) => {
    const chunks = references[0]?.content?.chunks;
    const docAggs = references[0]?.content?.docAggs;

    if (!chunks || !chunks[index]) {
      return null;
    }
    const doc = docAggs?.find((doc: any) => doc.docId === chunks[index].documentId);
    if (doc) {
      chunks[index].docUri = doc.docUri;
    }
    return chunks[index];
  }, [references]);

  /**
   * 判断是否为 PDF 文件
   */
  const isPdfFile = useCallback((fileName: string) => {
    return fileName?.toLowerCase().endsWith('.pdf');
  }, []);

  /**
   * 处理文档点击事件
   */
  const handleDocumentClick = useCallback((referenceInfo: any) => {
    const fileName = referenceInfo.documentName || '';
    const url = referenceInfo.docUri;

    if (!url) {
      console.warn('文档没有可用的链接');
      return;
    }

    if (isPdfFile(fileName)) {
      // PDF 文件处理 - 打开抽屉预览
      setCurrentPdfUrl(url);
      setCurrentPdfName(fileName);
      setCurrentChunk(referenceInfo);
      setPdfPreviewOpen(true);

    } else {
      // 其他文件类型，在新标签页中打开
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  }, [isPdfFile]);

  /**
   * 获取 Popover 内容
   */
  const getPopoverContent = useCallback((index: number) => {
    const referenceInfo = getReferenceInfo(index);
    if (!referenceInfo) {
      return <div className="p-2 text-sm text-muted-foreground">引用信息不可用</div>;
    }
    const imageUri = referenceInfo.imageUri;
    const content = referenceInfo.content;
    const documentName = referenceInfo.documentName;
    const hasUrl = !!referenceInfo.docUri ;

    return (
      <div className="p-4 w-[800px] max-w-[800px] max-h-[600px] overflow-y-auto">
        <div className="flex items-center gap-2 mb-3">
          <FileText className="h-4 w-4" />
          <span className="font-medium text-sm">引用内容</span>
        </div>

        <div className="flex gap-4">
          {/* 左侧图片 */}
          {imageUri && (
            <div className="flex-shrink-0 w-32 h-32">
              <Dialog>
                <DialogTrigger asChild>
                  <div className="relative group cursor-pointer">
                    <img
                      src={imageUri}
                      alt="引用图片"
                      className="w-full h-full object-cover rounded border bg-muted transition-all duration-200 group-hover:opacity-80"
                      onError={(e) => {
                        // 图片加载失败时显示占位符
                        const target = e.currentTarget;
                        target.style.display = 'none';
                        const placeholder = target.parentElement?.nextElementSibling as HTMLElement;
                        if (placeholder) {
                          placeholder.style.display = 'flex';
                        }
                      }}
                    />
                    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded flex items-center justify-center">
                      <ZoomIn className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] p-0">
                  <div className="relative">
                    <img
                      src={imageUri}
                      alt="引用图片放大"
                      className="w-full h-auto max-h-[85vh] object-contain"
                    />
                  </div>
                </DialogContent>
              </Dialog>
              <div
                className="w-full h-full bg-muted rounded border flex items-center justify-center text-xs text-muted-foreground"
                style={{ display: 'none' }}
              >
                图片
              </div>
            </div>
          )}

          {/* 右侧文字内容 */}
          <div className="flex-1 min-w-0">
            {/* 引用文本内容 */}
            {content && (
              <div className="text-sm text-foreground mb-2 whitespace-pre-wrap break-words">
                {content}
              </div>
            )}

            {/* 文档名称 */}
            {documentName && (
              <div className="text-xs text-muted-foreground">
                {hasUrl ? (
                  <button
                    onClick={() => handleDocumentClick(referenceInfo)}
                    className="flex items-center gap-1 hover:text-primary hover:underline cursor-pointer"
                  >
                    <span>{documentName}</span>
                    <ExternalLink className="h-3 w-3" />
                  </button>
                ) : (
                  <span>{documentName}</span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }, [getReferenceInfo, handleDocumentClick]);

  /**
   * 自定义文本渲染器，处理引用格式
   */
  const renderTextWithReferences = useCallback((text: string) => {
    const parts = [];
    let lastIndex = 0;
    let match;

    // 重置正则表达式的 lastIndex
    referenceRegex.lastIndex = 0;

    while ((match = referenceRegex.exec(text)) !== null) {
      // 添加引用前的文本
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index));
      }

      // 获取引用索引
      const index = getReferenceIndex(match[0]);

      // 创建引用按钮
      parts.push(
        <Popover key={`ref-${index}-${match.index}`}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-2 mx-1 text-xs bg-primary/10 hover:bg-primary/20 border-primary/30 rounded-md font-medium transition-all duration-200 hover:scale-105"
            >
              {index + 1}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto max-w-none p-0">
            {getPopoverContent(index)}
          </PopoverContent>
        </Popover>
      );

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts.length > 1 ? parts : text;
  }, [getPopoverContent]);

  return (
    <>
      <div className={cn("prose prose-sm dark:prose-invert max-w-none", className)}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeRaw, rehypeKatex]}
          components={{
          // 自定义代码块样式
          code: ({ node, className, children, ...props }: any) => {
            const inline = !className?.includes('language-')
            const match = /language-(\w+)/.exec(className || '')
           return match ? (
              <SyntaxHighlighter
                {...props}
                PreTag="div"
                language={match[1]}
                style={prism}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code {...props} className={`${className}`}>
                {children}
              </code>
            );
          },
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-border">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-border bg-muted px-4 py-2 text-left font-medium">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-border px-4 py-2">
              {children}
            </td>
          ),
          // 自定义链接样式
          a: ({ children, href }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              {children}
            </a>
          ),
          // 自定义引用块样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-primary pl-4 italic text-muted-foreground">
              {children}
            </blockquote>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-1">
              {children}
            </ol>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold mb-3 mt-5 first:mt-0">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-medium mb-2 mt-4 first:mt-0">
              {children}
            </h3>
          ),
          // 自定义段落样式，处理引用
          p: ({ children }) => {
            // 如果 children 是字符串，处理引用格式
            if (typeof children === 'string') {
              const processedChildren = renderTextWithReferences(children);
              return (
                <p className="leading-relaxed">
                  {processedChildren}
                </p>
              );
            }

            // 如果 children 是数组，递归处理每个元素
            if (Array.isArray(children)) {
              const processedChildren = children.map((child) => {
                if (typeof child === 'string') {
                  return renderTextWithReferences(child);
                }
                return child;
              });

              return (
                <p className="leading-relaxed">
                  {processedChildren}
                </p>
              );
            }

            return (
              <p className="leading-relaxed">
                {children}
              </p>
            );
          },
        }}
      >
        {contentWithCursor}
      </ReactMarkdown>
      </div>

      {/* PDF 预览组件 */}
     {pdfPreviewOpen && <PdfDrawer
        url={currentPdfUrl}
        fileName={currentPdfName}
        open={pdfPreviewOpen}
        onOpenChange={setPdfPreviewOpen}
        chunk={currentChunk}
      />}
    </>
  )
}
