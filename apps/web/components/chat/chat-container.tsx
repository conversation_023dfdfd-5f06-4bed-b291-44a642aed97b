"use client"

import { useState, useRef, useEffect, useMemo, useCallback } from "react"
import { ChatMessage } from "./chat-message"
import { ChatInput } from "./chat-input"
import { cn } from "@ragtop-web/ui/lib/utils"
import { Conversation } from "@/service/session-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { useSendMessageStream, useChatMessageState } from "@/hooks/use-chat-stream"
import { deduplicateArray } from "@/lib/chat-utils"
import { useChatHistory } from "@/hooks/use-chat-history"

interface ChatContainerProps {
  className?: string
  agentName?: string
  sessionId: string
  agentId: string
  agentPlaceholder?: string
}

export function ChatContainer({
  className,
  agentName = "助手",
  sessionId,
  agentId,
  agentPlaceholder
}: ChatContainerProps) {
  const [tempMessage, setTempMessage] = useState<Conversation[]>([])
  const [isAutoScrolling, setIsAutoScrolling] = useState(true)

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()
  const { sendMessage } = useSendMessageStream();

  // 使用新的聊天历史 Hook
  const {
    messages: historyMessages,
    isLoading: isLoadingHistory,
    hasMore,
    loadMore
  } = useChatHistory({
    sessionId,
    pageSize: 20,
    enabled: !!sessionId
  })

  const {
    message,
    isStreaming,
    questionId,
    startStream,
    cancelStream,
  } = useChatMessageState(sendMessage);

  const finalMessage = useMemo(()=>{
    return [...historyMessages, ...tempMessage]
  },[historyMessages, tempMessage])
console.log(message)
  useEffect(() => {
    setTempMessage((prev) =>
      prev.map((msg) =>
        msg.id === "template-question" ? { ...msg, id: questionId } : msg
      )
    );
  }, [questionId]);

  // 监听 message.content 变化并更新 tempMessage 中对应的项
  useEffect(() => {
    if(message?.id){
       setTempMessage((prev) => {
      return deduplicateArray([...prev,message], message.id)
    }
    );
    }
  }, [message?.content,message?.id]);

  // 自动滚动到底部当有新消息时
  useEffect(() => {
    if (scrollAreaRef.current && isAutoScrolling) {
      const scrollContainer = scrollAreaRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [finalMessage, isAutoScrolling])

  // 处理滚动事件，实现向上滚动加载更多
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget

    // 检测是否滚动到顶部，加载更多历史消息
    if (scrollTop === 0 && hasMore && !isLoadingHistory) {
      loadMore()
    }

    // 检测是否接近底部，启用自动滚动
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
    setIsAutoScrolling(isNearBottom)
  }, [hasMore, isLoadingHistory, loadMore])


  const handleSendMessage = (content: string) => {
    // 检查必要参数
    if (!sessionId || !agentId) {
      toast({
        title: "错误",
        description: "缺少会话或代理信息",
        variant: "destructive",
      })
      return
    }

    // Add user message
    const userMessage: Conversation = {
      id: `template-question`,
      role: "user",
      content,
    }

    setTempMessage((prev) => [...prev, userMessage])

    // 启用自动滚动（发送新消息时）
    setIsAutoScrolling(true)

    // 发送流式消息
    startStream({
      agent_id: agentId,
      session_id: sessionId,
      text_content: content,
    })
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div
        className="flex-1 overflow-y-auto"
        ref={scrollAreaRef}
        onScroll={handleScroll}
      >
        {finalMessage.length === 0 && !isLoadingHistory ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            开始新的对话...
          </div>
        ) : (
          <div>
            {/* 顶部加载更多指示器 */}
            {hasMore && (
              <div className="flex items-center justify-center p-4 text-muted-foreground">
                {isLoadingHistory ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                    <span>加载更多历史消息...</span>
                  </>
                ) : (
                  <span className="text-sm">向上滚动加载更多历史消息</span>
                )}
              </div>
            )}

            {/* 消息列表 */}
            {finalMessage?.map((message) => (
              <ChatMessage
                key={message.id}
                role={message.role}
                content={message.content}
                agentName={agentName}
                references={message.references}
                files={message.files}
              />
            ))}

            {/* 流式回复指示器 */}
            {isStreaming && (
              <div className="flex items-center space-x-2 p-4 text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                <span>正在生成回复...</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mt-auto pt-4">
        <ChatInput
        placeholder={agentPlaceholder}
          onSend={handleSendMessage}
          onCancel={cancelStream}
          disabled={isStreaming || isLoadingHistory}
        />
      </div>
    </div>
  )
}
