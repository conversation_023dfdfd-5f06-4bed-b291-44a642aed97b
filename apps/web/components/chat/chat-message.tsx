"use client"


import { Avatar, AvatarFallback } from "@ragtop-web/ui/components/avatar"
import { Separator } from "@ragtop-web/ui/components/separator"
import {
  User,
  Bo<PERSON>,
} from "lucide-react"
import { cn } from "@ragtop-web/ui/lib/utils"
import { MarkdownRenderer } from "@/components/markdown-renderer"

export type MessageRole = "user" | "assistant"

export interface ChatMessageProps {
  role: MessageRole
  content: string
  timestamp?: string
  // showActions?: boolean
  className?: string
  agentName?: string
  references?: any[]
  files?: any[]
}

export function ChatMessage({
  role,
  content,
  // showActions = true,
  className,
  agentName = "助手",
  references = [],
  files = []
}: ChatMessageProps) {

  return (
    <div className={cn("py-4", className)}>
      <Separator className="mb-4" />

      {/* 用户消息显示在右侧 */}
      {role === "user" ? (
        <div className="flex flex-col items-end">
          <div className="flex items-center mb-2">
            <span className="font-medium mr-2">You</span>
            <Avatar className="h-6 w-6">
              <AvatarFallback>
                <User className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
          </div>
          <div className="mt-2 max-w-[80%] bg-primary/10 p-3 rounded-lg">
            <div className="text-sm">{content}</div>
          </div>
        </div>
      ) : (
        /* Agent消息显示在左侧 */
        <div className="flex flex-col items-start">
          <div className="flex items-center mb-2">
            <Avatar className="h-6 w-6 mr-2">
              <AvatarFallback>
                <Bot className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <span className="font-medium">{agentName}</span>
          </div>
          <div className="mt-2 max-w-[80%] bg-muted p-3 rounded-lg">
            <MarkdownRenderer content={content} references={references} files={files} />
          </div>
        </div>
      )}

      {/* {role === "assistant" && (
        <>
          <div
            className="flex items-center mt-4 pl-8 text-sm text-muted-foreground cursor-pointer hover:text-foreground"
            onClick={() => setIsTaskStepsExpanded(!isTaskStepsExpanded)}
          >
            <LightbulbIcon className="h-4 w-4 mr-1" />
            <span>Task Steps</span>
            <ChevronDown className={cn(
              "h-4 w-4 ml-1 transition-transform",
              isTaskStepsExpanded ? "rotate-180" : ""
            )} />
          </div>

          {isTaskStepsExpanded && (
            <div className="mt-2 pl-8 py-2 text-sm">
              你好！有什么我可以帮助你的吗？
            </div>
          )}

          {showActions && (
            <div className="flex items-center mt-4 pl-8 space-x-2">
              <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full">
                <ThumbsUp className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full">
                <ThumbsDown className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full">
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full">
                <Quote className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" className="h-6 w-6 rounded-full">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </>
      )} */}
    </div>
  )
}
