"use client"

import { FileText, Download, ExternalLink } from "lucide-react"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import { Badge } from "@ragtop-web/ui/components/badge"
import { cn } from "@ragtop-web/ui/lib/utils"
import { getFileType, getFileIconColor } from "@/app/(basic)/files/utils"
import { downloadFile, getFileTypeFromName } from "@/lib/chat-utils"
import { MarkdownRenderer } from "./markdown-renderer"

/**
 * 文件内容接口
 */
export interface FileDisplayItem {
  title: string
  type: string
  uri: string
}

interface FileDisplayProps {
  files: FileDisplayItem[]
  className?: string
}

/**
 * 获取文件图标
 */
const getFileIcon = (fileType: string) => {
  return <FileText className={cn("h-4 w-4", getFileIconColor(fileType))} />
}

/**
 * 处理文件下载
 */
const handleFileDownload = (file: FileDisplayItem) => {
  downloadFile(file.uri, file.title)
}

/**
 * 文件展示组件
 *
 * 用于在聊天消息中展示文件列表，支持文件下载
 */
export function FileDisplay({ files, className }: FileDisplayProps) {
  if (!files || files.length === 0) {
    return null
  }
  console.log(files)

  return (
    <div className={cn("mt-3 space-y-2", className)}>
      <div className="text-xs text-muted-foreground font-medium">
        附件 ({files.length})
      </div>
      <div className="space-y-2">
        {files.map((file, index) => {
          const fileType = getFileTypeFromName(file.type)

          return (
            <div
              key={index}
              className="flex items-center justify-between p-2 bg-muted/50 rounded-md border border-border/50 hover:bg-muted/70 transition-colors"
            >
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {getFileIcon(fileType)}
                <div className="flex-1 min-w-0">
                    <MarkdownRenderer content={file.title} />
                </div>
              </div>

              <div className="flex items-center space-x-1 ml-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => handleFileDownload(file)}
                  title="下载文件"
                >
                  <Download className="h-4 w-4" />
                </Button>
                {/* <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => window.open(file.content.uri, '_blank', 'noopener,noreferrer')}
                  title="在新窗口打开"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button> */}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
