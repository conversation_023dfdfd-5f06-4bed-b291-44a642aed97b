"use client"

import { useState } from 'react'
import { Button } from '@ragtop-web/ui/components/button'
import { PdfPreviewer } from '@/components/pdf-previewer'
import { IReferenceChunk } from '@/service/session-service'

/**
 * PDF 预览测试页面
 */
export default function TestPdfPage() {
  const [isOpen, setIsOpen] = useState(false)

  // 模拟的 chunk 数据，包含高亮位置信息
  const mockChunk: IReferenceChunk = {
    id: "test-chunk-1",
    content: null,
    document_id: "test-doc-1",
    document_name: "测试文档.pdf",
    dataset_id: "test-dataset-1",
    image_id: "test-image-1",
    similarity: 0.95,
    vector_similarity: 0.92,
    term_similarity: 0.88,
    // 模拟高亮位置数据 [页码, x1, x2, y1, y2]
    positions: [
      [1, 100, 300, 150, 180], // 第1页的一个高亮区域
      [1, 120, 350, 200, 230], // 第1页的另一个高亮区域
    ]
  }

  return (
    <div className="container mx-auto p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">PDF 预览组件测试</h1>
        
        <div className="space-y-4">
          <div className="p-4 border rounded-lg">
            <h2 className="text-xl font-semibold mb-2">功能说明</h2>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>使用 react-pdf-highlighter 替代了原来的 iframe</li>
              <li>支持基于 chunk 数据的自动高亮显示</li>
              <li>支持缩放、旋转等基本操作</li>
              <li>支持用户选择文本并添加新高亮（按住 Alt 键可选择区域）</li>
              <li>点击高亮区域可查看详细信息</li>
            </ul>
          </div>

          <div className="p-4 border rounded-lg">
            <h2 className="text-xl font-semibold mb-2">测试操作</h2>
            <div className="space-y-2">
              <Button 
                onClick={() => setIsOpen(true)}
                className="w-full"
              >
                打开 PDF 预览（带高亮）
              </Button>
              
              <p className="text-sm text-gray-500">
                点击按钮将打开一个包含高亮标记的 PDF 预览窗口
              </p>
            </div>
          </div>

          <div className="p-4 border rounded-lg bg-yellow-50">
            <h2 className="text-xl font-semibold mb-2">注意事项</h2>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>确保 PDF URL 可以正常访问</li>
              <li>高亮位置基于 chunk.positions 数据自动生成</li>
              <li>如果 PDF 加载失败，请检查网络连接和 URL 有效性</li>
              <li>按住 Alt 键并拖拽可以选择区域进行高亮</li>
            </ul>
          </div>
        </div>

        <PdfPreviewer
          url="http://106.75.135.24:9380/api/v1/ragtop/document/get/a4f2779c3bbd11f0b2071a70a19bcd0e"
          fileName="测试文档.pdf"
          chunk={mockChunk}
          open={isOpen}
          onOpenChange={setIsOpen}
        />
      </div>
    </div>
  )
}
