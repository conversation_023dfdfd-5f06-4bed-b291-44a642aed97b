"use client"

import { useState, useEffect } from "react"
import { Chat<PERSON>ontainer } from "@/components/chat/chat-container"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useAgent } from "@/service/agent-service"
import { useSession, useSessionHistory, Conversation } from "@/service/session-service"
import { convertHistoryToMessages } from "@/lib/chat-utils"

interface SessionPageComponentProps {
  agentId: string
  sessionId: string
}

/**
 * Session详情页面组件
 * 集成真实的API调用，支持获取Agent、Session和历史消息
 */
export default function SessionPageComponent({ agentId, sessionId }: SessionPageComponentProps) {
  const [isInitialized, setIsInitialized] = useState(false)

  // API hooks - 延迟加载
  const {data: agent} = useAgent(agentId, isInitialized)
  const {data:session,isLoading } = useSession(sessionId, isInitialized)
  const { data: sessionHistory, isLoading: isHistoryLoading } = useSessionHistory(sessionId, 1, 20, isInitialized)

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true)
  }, [])



  // 构建聊天标题
  const chatTitle = agent && session
    ? `${agent.name} - ${session.title}`
    : "Ragtop 聊天"

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "Agent列表", href: "/", isCurrent: false },
    { title: agent?.name || "Agent", href: `/agent/${agentId}`, isCurrent: false },
    { title: session?.title || "会话", href: `/agent/${agentId}/${sessionId}`, isCurrent: true }
  ]

  if (isLoading) {
    return (
      <CustomContainer
        title="加载中..."
        // breadcrumbs={breadcrumbs}
        className="max-w-4xl mx-auto h-[calc(100vh-8rem)] py-4"
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
            <p className="text-sm text-muted-foreground">加载会话信息中...</p>
          </div>
        </div>
      </CustomContainer>
    )
  }

  if (!agent || !session) {
    return (
      <CustomContainer
        title="会话不存在"
        breadcrumbs={breadcrumbs}
        className="max-w-4xl mx-auto h-[calc(100vh-8rem)] py-4"
      >
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">未找到指定的会话</p>
        </div>
      </CustomContainer>
    )
  }

  // 获取历史消息
  const initialMessages = sessionHistory?.records
    ? convertHistoryToMessages(sessionHistory.records)
    : []
  return (
    <CustomContainer
      title={chatTitle}
      breadcrumbs={breadcrumbs}
      className="max-w-4xl mx-auto h-[calc(100vh-8rem)] py-4"
    >
      <ChatContainer
        initialMessages={initialMessages}
        agentName={agent?.name || "助手"}
        sessionId={sessionId}
        agentId={agentId}
        isLoadingHistory={isHistoryLoading}
        agentPlaceholder={agent?.hello_message || "正在加载..."}
      />
    </CustomContainer>
  )
}
