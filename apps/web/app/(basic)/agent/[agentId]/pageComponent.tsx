"use client"

import { useEffect, useState } from "react"
import { AgentDetails } from "@/components/agent/agent-details"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useAgent, useUpdateAgent, useDeleteAgent, Agents } from "@/service/agent-service"
import { useRouter } from "next/navigation"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { Button } from "@ragtop-web/ui/components/button"
import { Loader2, Pencil, Trash2 } from "lucide-react"
import { AgentDialog } from "@/components/agent/agent-dialog"
import { AlertDialog, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from "@ragtop-web/ui/components/alert-dialog"

interface AgentPageComponentProps {
  agentId: string
}

/**
 * Agent详情页面组件
 * 集成真实的API调用，支持获取、更新和删除Agent
 */
export default function AgentPageComponent({ agentId }: AgentPageComponentProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  // API hooks - 延迟加载
  const {data: agent,isFetching:isLoading} = useAgent(agentId, isInitialized)
  const updateAgent = useUpdateAgent(agentId)
  const deleteAgent = useDeleteAgent()

  // 页面初始化时触发数据加载
  useEffect(() => {
    setIsInitialized(true)
  }, [])

  // 处理更新Agent
  const handleUpdateAgent = async (data: any) => {
    try {
      await updateAgent.mutateAsync({ ...data, agent_id: agentId })
      toast({
        title: "成功",
        description: "Agent更新成功",
      })
      setIsEditDialogOpen(false)
    } catch (error) {
      console.error("更新Agent失败:", error)
      toast({
        title: "错误",
        description: "更新Agent失败",
        variant: "destructive",
      })
    }
  }

  // 处理删除Agent
  const handleDeleteAgent = async () => {
    try {
      await deleteAgent.mutateAsync({ agent_id: agentId })
      toast({
        title: "成功",
        description: "Agent删除成功",
      })
      setIsDeleteDialogOpen(false)
      router.push("/")
    } catch (error) {
      console.error("删除Agent失败:", error)
      toast({
        title: "错误",
        description: "删除Agent失败",
        variant: "destructive",
      })
    }
  }

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "Agent列表", href: "/", isCurrent: false },
    { title: agent?.name || "未命名Agent", href: `/agent/${agentId}`, isCurrent: true }
  ]

  if (isLoading) {
    return (
      <CustomContainer
        title="加载中..."
        breadcrumbs={breadcrumbs}
      >
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center gap-2">
            <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
            <p className="text-sm text-muted-foreground">加载Agent信息中...</p>
          </div>
        </div>
      </CustomContainer>
    )
  }

  if (!agent) {
    return (
      <CustomContainer
        title="Agent不存在"
        breadcrumbs={breadcrumbs}
      >
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">未找到指定的Agent</p>
        </div>
      </CustomContainer>
    )
  }

  return (
    <CustomContainer
      title={agent?.name || "未命名Agent"}
      breadcrumbs={breadcrumbs}
      action={
        <div className="flex gap-2">
          <Button
            onClick={() => setIsEditDialogOpen(true)}
            disabled={updateAgent.isPending || deleteAgent.isPending}
          >
            {updateAgent.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Pencil className="h-4 w-4" />
            )}
            编辑
          </Button>
          <Button
            onClick={() => setIsDeleteDialogOpen(true)}
            variant="destructive"
            disabled={updateAgent.isPending || deleteAgent.isPending}
          >
            {deleteAgent.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            删除
          </Button>
        </div>
      }
    >
      <AgentDetails
        agent={agent}
      />
      <AgentDialog
        open={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        onSubmit={handleUpdateAgent}
        initialData={agent}
      />
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这个Agent吗？此操作无法撤销，所有相关的会话和数据都将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteAgent}>
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </CustomContainer>
  )
}
