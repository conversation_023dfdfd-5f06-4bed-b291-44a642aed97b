"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2, Users } from "lucide-react"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import { Pagination } from "@ragtop-web/ui/components/pagination"
import { CustomDrawer } from "@ragtop-web/ui/components/custom-drawer"
import { TeamForm } from "./components/team-form"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { useTeams, useCreateTeam, useUpdateTeam, useDeleteTeam, TeamResponse } from "@/service/team-service"
import { useQueryUsers } from "@/service/user-service"
import { TeamMembersList } from "./components/team-members-list"

// 用户数据结构
export interface User {
  id: string
  login_name: string
  nick?: string
  create_time?: string
  roles?: Array<{
    id: string
    name: string
  }>
}

// 团队数据结构（用于表单）
export interface Team {
  id: string
  name: string
  admin_user_id: string
  members: string[] // 成员ID列表
}

export default function TeamsPage() {
  // 分页状态
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 获取团队列表
  const { data, isLoading, isFetching } = useTeams(pageNumber, pageSize)
  const teams = data?.records || []
  const totalItems = data?.total || 0

  // 加载状态
  const isTableLoading = isLoading || isFetching

  // 创建、更新和删除团队的mutation
  const createTeamMutation = useCreateTeam()
  const updateTeamMutation = useUpdateTeam()
  const deleteTeamMutation = useDeleteTeam()

  // UI状态
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [selectedTeam, setSelectedTeam] = useState<TeamResponse | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [teamToDelete, setTeamToDelete] = useState<TeamResponse | null>(null)
  const [viewMode, setViewMode] = useState<"edit" | "details">("edit")

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPageNumber(page)
  }

  // 处理打开团队详情抽屉
  const handleEditTeam = (team: TeamResponse) => {
    setSelectedTeam(team)
    setIsCreating(false)
    setViewMode("edit")
    setIsDrawerOpen(true)
  }

  // 处理查看团队详情
  const handleViewTeamDetails = (team: TeamResponse) => {
    setSelectedTeam(team)
    setViewMode("details")
    setIsDrawerOpen(true)
  }

  // 处理打开创建团队抽屉
  const handleCreateTeam = () => {
    setSelectedTeam(null)
    setIsCreating(true)
    setViewMode("edit")
    setIsDrawerOpen(true)
  }

  // 处理关闭抽屉
  const handleCloseDrawer = () => {
    setIsDrawerOpen(false)
  }

  // 处理保存团队
  const handleSaveTeam = (formData:any) => {
    if (isCreating) {
      // 创建新团队
      createTeamMutation.mutate(formData, {
        onSuccess: () => {
          setIsDrawerOpen(false)
        }
      })
    } else if (selectedTeam) {
      // 更新团队
      updateTeamMutation.mutate({
        team_id: selectedTeam.id,
        name: formData.name,
        admin_user_id: formData.admin_user_id
      }, {
        onSuccess: () => {
          setIsDrawerOpen(false)
        }
      })
    }
  }

  // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (team: TeamResponse) => {
    setTeamToDelete(team)
    setIsDeleteDialogOpen(true)
  }

  // 处理删除团队
  const handleDeleteTeam = () => {
    if (teamToDelete) {
      deleteTeamMutation.mutate({ team_id: teamToDelete.id }, {
        onSuccess: () => {
          setIsDeleteDialogOpen(false)
          setTeamToDelete(null)
        }
      })
    } else {
      setIsDeleteDialogOpen(false)
      setTeamToDelete(null)
    }
  }

  // 定义表格列
  const columns: ColumnDef<TeamResponse>[] = [
    {
      accessorKey: "name",
      header: "团队名称",
      cell: ({ row }) => (
        <div className="font-medium flex items-center gap-2">
          <Users className="h-4 w-4 text-muted-foreground" />
          {row.original.name}
        </div>
      ),
    },
    {
      accessorKey: "admin",
      header: "管理员",
      cell: ({ row }) => row.original.admin?.login_name || "未指定",
    },
    {
      accessorKey: "member_count",
      header: "成员数量",
      cell: ({ row }) => row.original.member_count,
    },
    {
      accessorKey: "create_time",
      header: "创建时间",
      cell: ({ row }) => {
        const createTime = row.original.create_time;
        if (!createTime) return "-";

        try {
          return new Date(createTime).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
        } catch (error) {
          return "-";
        }
      },
    },
    {
      id: "actions",
      header: () => <div className="w-[100px]">操作</div>,
      cell: ({ row }) => {
        const team = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleViewTeamDetails(team)}
            >
              <Users className="h-4 w-4" />
              <span className="sr-only">查看</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEditTeam(team)}
            >
              <Pencil className="h-4 w-4" />
              <span className="sr-only">编辑</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleOpenDeleteDialog(team)}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">删除</span>
            </Button>
          </div>
        );
      },
    },
  ];

  // 处理用户查询
  const { mutateAsync: queryUsers } = useQueryUsers()

  return (
    <CustomContainer
      title="团队管理"
      action={<Button onClick={handleCreateTeam}>
        <PlusIcon className="h-4 w-4" />
        添加团队
      </Button>}
    >
      <div className="rounded-md">
        {isTableLoading ? (
          <div className="flex items-center justify-center h-64 border rounded-md">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
              <p className="text-sm text-muted-foreground">加载中...</p>
            </div>
          </div>
        ) : (
          <DataTable columns={columns} data={teams} />
        )}
        <div className="w-full flex justify-center mt-4">
          <Pagination
            pageCount={Math.ceil(totalItems / pageSize)}
            currentPage={pageNumber}
            onPageChange={handlePageChange}
          />
        </div>
      </div>



      {/* 团队抽屉 */}
      <CustomDrawer
        open={isDrawerOpen}
        onClose={handleCloseDrawer}
        title={
          viewMode === "details"
            ? "团队详情"
            : isCreating
              ? "创建团队"
              : "编辑团队"
        }
      >
        {viewMode === "details" && selectedTeam ? (
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">团队名称</h3>
                  <p className="mt-1">{selectedTeam.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">管理员</h3>
                  <p className="mt-1">{selectedTeam.admin?.login_name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">成员数量</h3>
                  <p className="mt-1">{selectedTeam.member_count}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">创建时间</h3>
                  <p className="mt-1">
                    {new Date(selectedTeam.create_time).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>
            <TeamMembersList id={selectedTeam.id} />
          </div>
        ) : (
          <TeamForm
            queryUsers={queryUsers}
            teamData={isCreating ? undefined : {
              id: selectedTeam?.id,
              name: selectedTeam?.name || '',
              admin_user_id: selectedTeam?.admin?.id || ''
            }}
            onSave={handleSaveTeam}
            isCreating={isCreating}
          />
        )}
      </CustomDrawer>

      {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除团队 "{teamToDelete?.name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteTeam}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </CustomContainer>
  )
}
